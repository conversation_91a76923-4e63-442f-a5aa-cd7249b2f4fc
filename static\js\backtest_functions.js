// AI推理回测功能JavaScript文件

// 显示回测结果
function displayBacktestResults(result) {
    console.log('📊 显示回测结果:', result);

    // 显示回测结果卡片
    const backtestCard = document.getElementById('backtestCard');
    if (backtestCard) {
        backtestCard.style.display = 'block';
    }

    // 显示统计信息
    displayBacktestStats(result.statistics);

    // 显示详细结果
    if (result.trades && result.trades.length > 0) {
        displayTradeDetails(result.trades);
    }

    // 如果有优化结果，显示优化结果
    if (result.optimization_results) {
        displayOptimizationResults(result);
    }
}

// 显示回测统计
function displayBacktestStats(stats) {
    const statsContainer = document.getElementById('backtestStats');
    if (!statsContainer || !stats) return;

    // 安全地处理可能为undefined的值
    const totalProfit = stats.total_profit || 0;
    const winRate = stats.win_rate || 0;
    const profitFactor = stats.profit_factor || 0;
    const maxDrawdown = stats.max_drawdown || 0;
    const finalBalance = stats.final_balance || 0;

    const profitClass = totalProfit >= 0 ? 'profit-positive' : 'profit-negative';
    const profitIcon = totalProfit >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

    statsContainer.innerHTML = `
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value ${profitClass}">
                    <i class="fas ${profitIcon} me-1"></i>
                    $${totalProfit.toFixed(2)}
                </div>
                <div class="metric-label">总盈亏</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">${stats.total_trades || 0}</div>
                <div class="metric-label">总交易数</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value text-success">${stats.winning_trades || 0}</div>
                <div class="metric-label">盈利交易</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value text-danger">${stats.losing_trades || 0}</div>
                <div class="metric-label">亏损交易</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">${(winRate * 100).toFixed(1)}%</div>
                <div class="metric-label">胜率</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">${profitFactor.toFixed(2)}</div>
                <div class="metric-label">盈利因子</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">${maxDrawdown.toFixed(2)}%</div>
                <div class="metric-label">最大回撤</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">$${finalBalance.toFixed(2)}</div>
                <div class="metric-label">最终余额</div>
            </div>
        </div>
    `;
}

// 显示交易详情
function displayTradeDetails(trades) {
    const resultsContainer = document.getElementById('backtestResults');
    if (!resultsContainer) return;

    resultsContainer.innerHTML = `
        <h6 class="text-primary mb-3">
            <i class="fas fa-list me-2"></i>交易详情 (${trades.length} 笔交易)
        </h6>
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>时间</th>
                        <th>类型</th>
                        <th>手数</th>
                        <th>开仓价</th>
                        <th>平仓价</th>
                        <th>止损</th>
                        <th>止盈</th>
                        <th>盈亏</th>
                        <th>置信度</th>
                    </tr>
                </thead>
                <tbody>
                    ${trades.map(trade => {
                        const profitClass = trade.profit >= 0 ? 'trade-row-profit' : 'trade-row-loss';
                        const profitText = trade.profit >= 0 ? 'profit-positive' : 'profit-negative';
                        return `
                            <tr class="${profitClass}">
                                <td>${new Date(trade.open_time).toLocaleString()}</td>
                                <td>
                                    <span class="badge ${trade.type === 'BUY' ? 'bg-success' : 'bg-danger'}">
                                        ${trade.type}
                                    </span>
                                </td>
                                <td>${trade.lot_size || 0}</td>
                                <td>${trade.open_price ? trade.open_price.toFixed(5) : 'N/A'}</td>
                                <td>${trade.close_price ? trade.close_price.toFixed(5) : 'N/A'}</td>
                                <td>${trade.stop_loss ? trade.stop_loss.toFixed(5) : 'N/A'}</td>
                                <td>${trade.take_profit ? trade.take_profit.toFixed(5) : 'N/A'}</td>
                                <td class="${profitText}">$${trade.profit ? trade.profit.toFixed(2) : '0.00'}</td>
                                <td>${trade.confidence ? (trade.confidence * 100).toFixed(1) : '0.0'}%</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
    `;
}

// 参数优化
async function startParameterOptimization() {
    console.log('🔄 开始参数优化...');

    if (!selectedModel) {
        showError('请先选择一个模型');
        return;
    }

    const formData = getFormData();
    if (!formData.symbol || !formData.timeframe) {
        showError('请填写完整的基础配置');
        return;
    }

    try {
        updateStatus('running', '正在执行参数优化...');
        document.getElementById('parameterOptimizationBtn').disabled = true;

        const optimizationData = {
            model_id: selectedModel.id,
            symbol: formData.symbol,
            timeframe: formData.timeframe,
            optimization_period: document.getElementById('optimizationPeriod').value,
            risk_preference: document.getElementById('riskPreference').value
        };

        const response = await fetch('/api/deep-learning/parameter-optimization', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(optimizationData)
        });

        const result = await response.json();

        if (result.success) {
            displayOptimizationResults(result);
            updateStatus('completed', '参数优化完成');
            showSuccess('参数优化完成！');
        } else {
            showError(`参数优化失败: ${result.error}`);
            updateStatus('error', '参数优化失败');
        }

    } catch (error) {
        console.error('参数优化失败:', error);
        showError(`参数优化失败: ${error.message}`);
        updateStatus('error', '参数优化失败');
    } finally {
        document.getElementById('parameterOptimizationBtn').disabled = false;
    }
}

// 显示优化结果
function displayOptimizationResults(result) {
    const resultsContainer = document.getElementById('backtestResults');
    if (!resultsContainer) return;

    const report = result.optimization_report || {};
    const results = result.optimization_results || [];

    resultsContainer.innerHTML = `
        <h6 class="text-primary mb-3">
            <i class="fas fa-cogs me-2"></i>参数优化结果
        </h6>
        
        <!-- 优化结果表格 -->
        <div class="table-responsive mb-4">
            <table class="table table-striped">
                <thead class="table-primary">
                    <tr>
                        <th>排名</th>
                        <th>总盈亏</th>
                        <th>胜率</th>
                        <th>盈利因子</th>
                        <th>最大回撤</th>
                        <th>手数</th>
                        <th>止损</th>
                        <th>止盈</th>
                        <th>置信度</th>
                        <th>移动止损</th>
                        <th>触发距离</th>
                        <th>跟踪步长</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${results.slice(0, 10).map((result, index) => {
                        const rankClass = index < 3 ? `optimization-rank-${index + 1}` : '';
                        return `
                            <tr class="${rankClass}">
                                <td>
                                    <strong>#${index + 1}</strong>
                                    ${index === 0 ? '<i class="fas fa-crown text-warning ms-1"></i>' : ''}
                                </td>
                                <td class="${(result.statistics?.total_profit || 0) >= 0 ? 'profit-positive' : 'profit-negative'}">
                                    $${(result.statistics?.total_profit || 0).toFixed(2)}
                                </td>
                                <td>${((result.statistics?.win_rate || 0) * 100).toFixed(1)}%</td>
                                <td>${(result.statistics?.profit_factor || 0).toFixed(2)}</td>
                                <td>${(result.statistics?.max_drawdown || 0).toFixed(2)}%</td>
                                <td>${result.parameters?.lot_size || 0}</td>
                                <td>${result.parameters?.stop_loss_pips || 0}</td>
                                <td>${result.parameters?.take_profit_pips || 0}</td>
                                <td>${((result.parameters?.min_confidence || 0) * 100).toFixed(0)}%</td>
                                <td>
                                    ${result.parameters.trailing_stop_enabled ? 
                                        '<span class="badge bg-success">启用</span>' : 
                                        '<span class="badge bg-secondary">禁用</span>'}
                                </td>
                                <td>${result.parameters.trailing_stop_distance} pips</td>
                                <td>${result.parameters.trailing_stop_step} pips</td>
                                <td>
                                    <button class="btn btn-sm btn-primary"
                                            onclick="applyOptimizedParameters(${JSON.stringify(result.parameters).replace(/"/g, '&quot;')})">
                                        应用
                                    </button>
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>

        <!-- 参数建议 -->
        ${report.recommendations ? `
        <div class="alert alert-info">
            <h6 class="alert-heading">
                <i class="fas fa-lightbulb me-2"></i>优化建议
            </h6>
            <ul class="mb-0">
                ${report.recommendations.map(rec => `<li>${rec}</li>`).join('')}
            </ul>
        </div>
        ` : ''}
    `;

    // 显示回测结果卡片
    const backtestCard = document.getElementById('backtestCard');
    if (backtestCard) {
        backtestCard.style.display = 'block';
    }
}

// 应用优化后的参数
function applyOptimizedParameters(params) {
    console.log('📝 应用优化参数:', params);

    // 应用到回测配置
    document.getElementById('backtestLotSize').value = params.lot_size;
    document.getElementById('backtestStopLoss').value = params.stop_loss_pips;
    document.getElementById('backtestTakeProfit').value = params.take_profit_pips;
    document.getElementById('backtestMinConfidence').value = params.min_confidence;

    // 应用移动止损设置
    const trailingStopCheckbox = document.getElementById('enableTrailingStop');
    if (trailingStopCheckbox && params.trailing_stop_enabled !== undefined) {
        trailingStopCheckbox.checked = params.trailing_stop_enabled;

        if (params.trailing_stop_enabled) {
            document.getElementById('trailingStopDistance').value = params.trailing_stop_distance || 20;
            document.getElementById('trailingStopStep').value = params.trailing_stop_step || 10;
        }

        toggleTrailingStopConfig(); // 更新配置面板显示
    }

    // 应用悬崖勒马设置
    const cliffBrakeCheckbox = document.getElementById('backtestCliffBrake');
    if (cliffBrakeCheckbox) {
        cliffBrakeCheckbox.checked = params.cliff_brake_enabled;
    }

    showSuccess('参数已应用到回测配置中！包含移动止损和悬崖勒马设置');
}

// 加载保存的参数优化结果
async function loadSavedOptimizationResults() {
    if (!selectedModel) {
        return;
    }

    try {
        const riskPreference = document.getElementById('riskPreference').value;

        const response = await fetch(`/api/deep-learning/saved-optimization-results?model_id=${selectedModel.id}&symbol=${selectedModel.symbol}&timeframe=${selectedModel.timeframe}&risk_preference=${riskPreference}`);
        const result = await response.json();

        if (result.success) {
            console.log('✅ 找到保存的优化结果:', result);
            showInfo(`加载了保存的参数优化结果 (${new Date(result.created_at).toLocaleString()})`);

            // 显示保存的结果
            displayOptimizationResults(result);

            // 显示加载提示
            const backtestCard = document.getElementById('backtestCard');
            if (backtestCard) {
                const loadedBadge = document.createElement('div');
                loadedBadge.className = 'alert alert-success alert-dismissible fade show mt-2';
                loadedBadge.innerHTML = `
                    <i class="fas fa-history me-2"></i>
                    <strong>已加载保存的优化结果</strong> - 创建时间: ${new Date(result.created_at).toLocaleString()}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                backtestCard.insertBefore(loadedBadge, backtestCard.firstChild);
            }
        } else {
            console.log('ℹ️ 未找到保存的优化结果');
        }
    } catch (error) {
        console.error('加载保存的优化结果失败:', error);
    }
}
