#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的特征工程服务 - 专为深度学习模型优化
实现您提到的所有优化建议：
1. 增强的布林带特征 (挤压信号、突破信号)
2. ATR (平均真实波幅)
3. 随机指标 (Stochastic Oscillator)
4. 组合信号和确认机制
5. 动态风险管理
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import logging
from services.technical_indicators import TechnicalIndicators

logger = logging.getLogger(__name__)

class EnhancedFeatureEngineering:
    """增强的特征工程服务"""
    
    def __init__(self):
        self.ti = TechnicalIndicators()
    
    def calculate_all_enhanced_features(self, price_data: np.ndarray) -> Dict[str, np.ndarray]:
        """
        计算所有增强的技术指标特征
        
        Args:
            price_data: 价格数据 [open, high, low, close, volume]
            
        Returns:
            Dict: 包含所有特征的字典
        """
        logger.info("🔧 开始计算增强技术指标特征...")
        
        # 提取价格数据
        open_prices = pd.Series(price_data[:, 0])
        high_prices = pd.Series(price_data[:, 1])
        low_prices = pd.Series(price_data[:, 2])
        close_prices = pd.Series(price_data[:, 3])
        volumes = pd.Series(price_data[:, 4]) if price_data.shape[1] > 4 else None
        
        features = {}
        
        # 1. 增强的布林带特征
        logger.info("📈 计算增强布林带特征...")
        bb_features = self.ti.enhanced_bollinger_bands(close_prices)
        for key, value in bb_features.items():
            features[f'bb_{key}'] = value.fillna(0).values
        
        # 2. 增强的随机指标特征
        logger.info("🎯 计算增强随机指标特征...")
        stoch_features = self.ti.enhanced_stochastic(high_prices, low_prices, close_prices)
        for key, value in stoch_features.items():
            features[f'stoch_{key}'] = value.fillna(0).values
        
        # 3. 增强的ATR特征
        logger.info("📏 计算增强ATR特征...")
        atr_features = self.ti.enhanced_atr(high_prices, low_prices, close_prices)
        for key, value in atr_features.items():
            features[f'atr_{key}'] = value.fillna(0).values
        
        # 4. 组合信号
        logger.info("🔗 计算组合信号...")
        combined_features = self.ti.combined_signals(high_prices, low_prices, close_prices)
        for key, value in combined_features.items():
            features[f'combined_{key}'] = value.astype(int).values
        
        # 5. 市场状态分析
        logger.info("🌊 计算市场状态特征...")
        market_features = self.ti.market_regime_analysis(high_prices, low_prices, close_prices, volumes)
        for key, value in market_features.items():
            if isinstance(value, pd.Series):
                if value.dtype == 'object':
                    # 将分类特征转换为数值
                    features[f'market_{key}'] = pd.Categorical(value).codes
                else:
                    features[f'market_{key}'] = value.fillna(0).values
        
        # 6. 基础价格特征 (标准化)
        logger.info("💰 计算基础价格特征...")
        price_features = self._calculate_price_features(price_data)
        features.update(price_features)
        
        # 7. 动量和趋势特征
        logger.info("🚀 计算动量和趋势特征...")
        momentum_features = self._calculate_momentum_features(close_prices)
        features.update(momentum_features)
        
        logger.info(f"✅ 特征计算完成，共生成 {len(features)} 个特征")
        return features
    
    def _calculate_price_features(self, price_data: np.ndarray) -> Dict[str, np.ndarray]:
        """计算基础价格特征"""
        features = {}
        
        # 价格变化率
        for i, name in enumerate(['open', 'high', 'low', 'close']):
            prices = price_data[:, i]
            returns = np.diff(prices) / prices[:-1]
            returns = np.concatenate([[0], returns])  # 添加第一个值
            features[f'{name}_returns'] = returns
        
        # 价格相对位置 (收盘价在当日价格范围内的位置)
        high_low_range = price_data[:, 1] - price_data[:, 2]
        close_position = np.where(high_low_range != 0, 
                                (price_data[:, 3] - price_data[:, 2]) / high_low_range, 
                                0.5)
        features['close_position'] = close_position
        
        # 价格间隙 (开盘价与前一日收盘价的差异)
        gap = np.diff(price_data[:, 0]) / price_data[:-1, 3]
        gap = np.concatenate([[0], gap])
        features['price_gap'] = gap
        
        return features
    
    def _calculate_momentum_features(self, close_prices: pd.Series) -> Dict[str, np.ndarray]:
        """计算动量和趋势特征"""
        features = {}
        
        # 多周期动量
        for period in [5, 10, 20]:
            momentum = close_prices.pct_change(period)
            features[f'momentum_{period}'] = momentum.fillna(0).values
        
        # 价格加速度 (二阶导数)
        returns = close_prices.pct_change()
        acceleration = returns.diff()
        features['price_acceleration'] = acceleration.fillna(0).values
        
        # 趋势强度 (基于线性回归斜率)
        trend_strength = self._calculate_trend_strength(close_prices)
        features['trend_strength'] = trend_strength
        
        return features
    
    def _calculate_trend_strength(self, prices: pd.Series, window: int = 20) -> np.ndarray:
        """计算趋势强度"""
        trend_strength = np.zeros(len(prices))
        
        for i in range(window, len(prices)):
            y = prices.iloc[i-window:i].values
            x = np.arange(window)
            
            # 计算线性回归斜率
            slope = np.polyfit(x, y, 1)[0]
            trend_strength[i] = slope / prices.iloc[i] if prices.iloc[i] != 0 else 0
        
        return trend_strength
    
    def prepare_features_for_model(self, features_dict: Dict[str, np.ndarray], 
                                 feature_selection: Optional[List[str]] = None) -> np.ndarray:
        """
        为深度学习模型准备特征矩阵
        
        Args:
            features_dict: 特征字典
            feature_selection: 选择的特征列表 (None表示使用所有特征)
            
        Returns:
            np.ndarray: 标准化的特征矩阵
        """
        logger.info("🤖 为深度学习模型准备特征矩阵...")
        
        # 选择特征
        if feature_selection is None:
            selected_features = list(features_dict.keys())
        else:
            selected_features = [f for f in feature_selection if f in features_dict]
        
        logger.info(f"📊 选择了 {len(selected_features)} 个特征")
        
        # 构建特征矩阵
        feature_arrays = []
        feature_names = []
        
        for feature_name in selected_features:
            feature_data = features_dict[feature_name]
            
            # 确保是1维数组
            if len(feature_data.shape) > 1:
                feature_data = feature_data.flatten()
            
            # 检查数据有效性
            if np.any(np.isnan(feature_data)) or np.any(np.isinf(feature_data)):
                logger.warning(f"⚠️ 特征 {feature_name} 包含无效值，进行清理")
                feature_data = np.nan_to_num(feature_data, nan=0.0, posinf=0.0, neginf=0.0)
            
            # 标准化特征
            feature_mean = feature_data.mean()
            feature_std = feature_data.std()
            
            if feature_std > 1e-10:
                normalized_feature = (feature_data - feature_mean) / feature_std
            else:
                logger.warning(f"⚠️ 特征 {feature_name} 标准差过小，只进行中心化")
                normalized_feature = feature_data - feature_mean
            
            feature_arrays.append(normalized_feature.reshape(-1, 1))
            feature_names.append(feature_name)
        
        # 合并所有特征
        if feature_arrays:
            feature_matrix = np.concatenate(feature_arrays, axis=1)
            logger.info(f"✅ 特征矩阵构建完成: {feature_matrix.shape}")
            logger.info(f"📋 特征列表: {feature_names[:10]}{'...' if len(feature_names) > 10 else ''}")
            return feature_matrix
        else:
            logger.error("❌ 没有有效特征可用")
            raise ValueError("没有有效特征可用于模型训练")
    
    def get_recommended_features(self) -> List[str]:
        """
        获取推荐的特征列表 (基于重要性排序)
        
        Returns:
            List[str]: 推荐特征列表
        """
        return [
            # 布林带核心特征
            'bb_percent_b', 'bb_band_width', 'bb_squeeze', 'bb_breakout',
            'bb_distance_from_middle', 'bb_percent_b_change',
            
            # ATR核心特征
            'atr_atr', 'atr_ratio', 'atr_percentile', 'atr_change',
            'atr_low_volatility', 'atr_high_volatility',
            
            # 随机指标核心特征
            'stoch_stoch_k', 'stoch_stoch_d', 'stoch_k_d_diff',
            'stoch_overbought', 'stoch_oversold',
            
            # 组合信号
            'combined_squeeze_low_vol', 'combined_breakout_confirmed',
            'combined_bullish_confluence', 'combined_bearish_confluence',
            
            # 市场状态
            'market_trend_strength', 'market_trending_market', 'market_trend_direction',
            
            # 价格特征
            'close_returns', 'close_position', 'price_gap',
            
            # 动量特征
            'momentum_5', 'momentum_10', 'momentum_20', 'trend_strength'
        ]
    
    def create_feature_importance_analysis(self, features_dict: Dict[str, np.ndarray], 
                                         target: np.ndarray) -> Dict[str, float]:
        """
        分析特征重要性 (简单的相关性分析)
        
        Args:
            features_dict: 特征字典
            target: 目标变量
            
        Returns:
            Dict[str, float]: 特征重要性分数
        """
        logger.info("📊 分析特征重要性...")
        
        importance_scores = {}
        
        for feature_name, feature_data in features_dict.items():
            try:
                # 计算与目标变量的相关性
                correlation = np.corrcoef(feature_data, target)[0, 1]
                if np.isnan(correlation):
                    correlation = 0.0
                
                importance_scores[feature_name] = abs(correlation)
                
            except Exception as e:
                logger.warning(f"⚠️ 计算特征 {feature_name} 重要性失败: {e}")
                importance_scores[feature_name] = 0.0
        
        # 按重要性排序
        sorted_features = sorted(importance_scores.items(), key=lambda x: x[1], reverse=True)
        
        logger.info("🏆 特征重要性排名 (前10):")
        for i, (feature, score) in enumerate(sorted_features[:10]):
            logger.info(f"   {i+1}. {feature}: {score:.4f}")
        
        return importance_scores
